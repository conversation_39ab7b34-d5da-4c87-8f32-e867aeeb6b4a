interface Env {
  URL: {
    get(key: string): Promise<string | null>;
    put(key: string, value: string): Promise<void>;
  };
}

interface ShortenRequest {
  url: string;
}

function generateShortCode(): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < 8; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

function isValidUrl(url: string): boolean {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

export async function onRequestPost(context: { request: Request; env: Env }): Promise<Response> {
  const { request, env } = context;
  
  try {
    // Parse request body
    const body: ShortenRequest = await request.json();
    
    if (!body.url) {
      return new Response(JSON.stringify({ error: 'URL is required' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Validate URL
    if (!isValidUrl(body.url)) {
      return new Response(JSON.stringify({ error: 'Invalid URL format' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Check if URL already exists in KV
    // We'll use a hash of the URL as a key to check for duplicates
    const urlHash = await crypto.subtle.digest('SHA-256', new TextEncoder().encode(body.url));
    const urlHashHex = Array.from(new Uint8Array(urlHash))
      .map(b => b.toString(16).padStart(2, '0'))
      .join('');
    
    const existingCode = await env.URL.get(`hash:${urlHashHex}`);
    
    if (existingCode) {
      // URL already exists, return existing short URL
      return new Response(`https://menuhub.me/api/r/${existingCode}`, {
        status: 200,
        headers: { 'Content-Type': 'text/plain' }
      });
    }

    // Generate new short code and ensure it's unique
    let shortCode: string;
    let attempts = 0;
    do {
      shortCode = generateShortCode();
      attempts++;
      
      // Prevent infinite loop
      if (attempts > 10) {
        return new Response(JSON.stringify({ error: 'Unable to generate unique short code' }), {
          status: 500,
          headers: { 'Content-Type': 'application/json' }
        });
      }
    } while (await env.URL.get(`code:${shortCode}`));

    // Store both mappings in KV
    // code:shortCode -> original URL (for redirects)
    await env.URL.put(`code:${shortCode}`, body.url);
    
    // hash:urlHash -> shortCode (for duplicate prevention)
    await env.URL.put(`hash:${urlHashHex}`, shortCode);

    // Return shortened URL
    return new Response(`https://menuhub.me/api/r/${shortCode}`, {
      status: 201,
      headers: { 'Content-Type': 'text/plain' }
    });

  } catch (error) {
    console.error('Error in shorten function:', error);
    return new Response(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

// Handle CORS for development
export async function onRequestOptions(): Promise<Response> {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
} 