import { Restaurant, Category, Ingredient } from "./commonTypes";

export interface TableData {
  data?: Table;
}

export interface Table {
  id?: number;
  title?: string;
  restaurant?: Restaurant;
  items?: Item[];
}

export interface Item {
  sound: string;
  id?: number;
  restaurant_id?: number;
  price?: string;
  timer?: string;
  image?: string;
  status?: number;
  title?: string;
  category?: Category[];
  ingredient?: Ingredient[];
  translations?: ItemTranslation[];
  audio?: string;
  rating?: { rate: number }[];
}

export interface ItemTranslation {
  id?: number;
  locale?: string;
  item_id?: number;
  title?: string;
}
