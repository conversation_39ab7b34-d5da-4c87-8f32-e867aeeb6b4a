interface Env {
  URL: {
    get(key: string): Promise<string | null>;
    put(key: string, value: string): Promise<void>;
  };
}

export async function onRequestGet(context: { 
  request: Request; 
  env: Env; 
  params: { code: string } 
}): Promise<Response> {
  const { env, params } = context;
  
  try {
    const shortCode = params.code;
    
    if (!shortCode) {
      return new Response('Short code not provided', {
        status: 400,
        headers: { 'Content-Type': 'text/plain' }
      });
    }

    // Retrieve the original URL from KV
    const originalUrl = await env.URL.get(`code:${shortCode}`);
    
    if (!originalUrl) {
      return new Response('Short URL not found', {
        status: 404,
        headers: { 'Content-Type': 'text/plain' }
      });
    }

    // Redirect to the original URL
    return new Response(null, {
      status: 302,
      headers: {
        'Location': originalUrl,
        'Cache-Control': 'public, max-age=86400', // Cache for 24 hours
      }
    });

  } catch (error) {
    console.error('Error in redirect function:', error);
    return new Response('Internal server error', {
      status: 500,
      headers: { 'Content-Type': 'text/plain' }
    });
  }
}

// Handle CORS for development
export async function onRequestOptions(): Promise<Response> {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
} 