{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "devDependencies": {"@nuxtjs/google-fonts": "^3.0.1", "@nuxtjs/i18n": "8.0.0-beta.10", "@nuxtjs/tailwindcss": "^6.8.0", "@types/uuid": "^9.0.2", "nuxt": "^3.6.1", "nuxt-icon": "^0.5.0"}, "dependencies": {"@vueuse/core": "^10.2.1", "flowbite": "^1.8.1", "flowbite-vue": "^0.0.17-next.0", "nuxt-swiper": "^1.1.0", "uuid": "^9.0.0", "vue-tel-input": "^8.1.4", "vue-toastification": "^2.0.0-rc.5"}}