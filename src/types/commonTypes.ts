export interface Currency {
  id?: number;
  name: string;
  shortName: string;
  symbol: string;
  created_at?: string;
  updated_at?: string;
}

export interface Restaurant {
  uuid?: string;
  title?: string;
  slug?: string;
  address?: string;
  admin_phone?: string;
  open_from?: string;
  open_to?: string;
  created_at?: string;
  updated_at?: string;
  translations?: RestaurantTranslation[];
  fee?: number;
  currency?: Currency;
}

export interface RestaurantTranslation {
  id?: number;
  locale?: string;
  restaurant_id?: number;
  title?: string;
  address?: string;
}

export interface Category {
  id?: number;
  restaurant_id?: number;
  title?: string;
  description?: string;
  created_at?: string;
  updated_at?: string;
  translations?: CategoryTranslation[];
}

export interface CategoryTranslation {
  id?: number;
  locale?: string;
  category_id?: number;
  title?: string;
}

export interface CategoryList {
  name: string;
  count: number;
  data: Category;
}

export interface Ingredient {
  id?: number;
  title?: string;
  restaurant_id?: number;
  stock?: string;
  unit?: string;
  status?: string;
  created_at?: string;
  updated_at?: string;
  translations: IngredientTranslation[];
}

export interface IngredientTranslation {
  id?: number;
  locale?: string;
  ingredient_id?: number;
  title?: string;
}

export interface Pivot {
  cart_id?: number;
  dish_id?: number;
  quantity?: number;
}

export interface Dish {
  id?: number;
  restaurant_id?: number;
  title?: string;
  price?: string;
  timer?: string;
  image?: string;
  status?: number;
  created_at?: string;
  updated_at?: string;
  pivot?: Pivot;
  category?: Category[];
  ingredient?: Ingredient[];
  translations?: DishTranslation[];
  quantity?: number;
}

export interface DishTranslation {
  id?: number;
  locale?: string;
  dish_id?: number;
  title?: string;
}
