// https://nuxt.com/docs/api/configuration/nuxt-config
import { process } from "unenv/runtime/node/process/_process";

export default defineNuxtConfig({
  srcDir: "src/",
  vite: {
    server: {
      hmr: {
        clientPort: 24600,
        port: 24600,
      },
    },
  },
  app: {
    head: {
      link: [
        // { rel: 'stylesheet', href: "/assets/css/bootstrap.min.css" },
        // { rel: 'stylesheet', href: "/assets/font-awesome/4.2.0/css/font-awesome.min.css" },
        // { rel: 'stylesheet', href: "/assets/fonts/fonts.googleapis.com.css" },
        // { rel: 'stylesheet', href: "/assets/css/ace.min.css" },
        // { rel: 'stylesheet', href: "/assets/css/ekko-lightbox.min.css" },
        // { rel: "stylesheet", href: "/css/osahan.css" },
        { rel: "stylesheet", href: "/css/vendor/icofont/icofont.min.css" },
        { rel: "stylesheet", href: "/css/vendor/fontawesome/all.min.css" },
        // { rel: "stylesheet", href: "/css/vendor/bootstrap/bootstrap.min.css" },
        { rel: "stylesheet", href: "/css/input.css" },
      ],
      script: [
        // { src: '/assets/js/html5shiv.min.js' },
        // { src: '/assets/js/respond.min.js' },
        // { src: '/assets/js/jquery.2.1.1.min.js' },
        // { src: '/assets/js/bootstraps.js' },
        // { src: '/assets/js/jqueries.js' },
        // { src: '/assets/js/select2.min.js' },
        // { src: '/assets/js/fuelux.spinner.min.js' },
        // { src: '/assets/js/ace-elements.min.js' },
        // { src: '/assets/js/ace-editable.min.js' },
        // { src: '/assets/js/ace.min.js' },
        // { src: '/assets/js/ekko-lightbox.min.js' },
        {
          src: "https://js-eu1.hsforms.net/forms/embed/v2.js",
        },
        {
          src: "https://js.stripe.com/v3/pricing-table.js",
          async: true,
        },
      ],
      style: [],
    },
  },
  imports: {
    dirs: ["src/composables", "src/components/newcomponents"],
  },
  dir: {
    public: "../public/",
  },
  css: [
    // "/assets/css/bootstrap.min.css",
    // "/assets/font-awesome/4.2.0/css/font-awesome.min.css",
    // "/assets/fonts/fonts.googleapis.com.css",
    // "/assets/css/ace.min.css"
    // '/css/osahan',
    // '/css/vendor/icofont/icofont.min',
    // '/css/vendor/fontawesome/all.min',
    // '/css/vendor/bootstrap/bootstrap.min',
  ],
  modules: [
    // '@nuxtjs/moment',
    // 'dropzone-nuxt',
    "nuxt-swiper",
    "@nuxtjs/i18n",
    "@nuxtjs/google-fonts",
    "@nuxtjs/tailwindcss",
    "nuxt-icon",
  ],
  googleFonts: {
    families: {
      Allison: true,
      Quicksand: [100, 200, 300, 400, 500, 600, 700],
      NatoSansGeorgian: [100, 200, 300, 400, 500, 600, 700],
    },
  },
  // i18n: {
  //   locales: [
  //     {
  //       code: "en",
  //       name: "English",
  //       file: "en.ts",
  //     },
  //     {
  //       code: "ka",
  //       name: "Georgian",
  //       file: "ka.ts",
  //     },
  //     {
  //       code: "ru",
  //       name: "Russian",
  //       file: "ru.ts",
  //     },
  //     {
  //       code: "el",
  //       name: "Greek",
  //       file: "el.ts",
  //     },
  //   ],
  //   defaultLocale: "ka",
  //   langDir: "locales",
  //   detectBrowserLanguage: false,
  // },
  plugins: ["src/plugins/i18n.ts"],
  runtimeConfig: {
    apiSecret: "123",
    public: {
      apiBase: process.env.NUXT_PUBLIC_API_BASE,
      ablyKey: process.env.NUXT_PUBLIC_ABLY_KEY,
      audioBase: process.env.NUXT_PUBLIC_AUDIO_BASE,
      domainBase: process.env.NUXT_PUBLIC_DOMAIN_BASE,
      domainEmail: process.env.NUXT_PUBLIC_DOMAIN_EMAIL,
      domainPhone: process.env.NUXT_PUBLIC_PHONE,
      domainAddress: process.env.NUXT_PUBLIC_ADDRESS,
      quarterlyPrice: process.env.NUXT_PUBLIC_MONTHLY_PRICE,
      yearlyPrice: process.env.NUXT_PUBLIC_QUARTERLY_PRICE,
      monthlyPrice: process.env.NUXT_PUBLIC_YEARLY_PRICE,
    },
  },
  build: {
    transpile: ["vue-toastification"],
  },
  components: ["~/components"],
});
