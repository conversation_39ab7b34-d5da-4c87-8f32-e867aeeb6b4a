<template>
  <section class="pb-16 bg-white px-8" id="contact">
    <div class="container mx-auto flex items-center flex-col">
      <div class="text-center w-full mb-8">
        <CustomHeading :text="$t('contactUs')" />
      </div>
      <div v-if="loading">
        <div class="text-center blue">
          <i class="fa fa-spin fa-spinner fa-5x"></i>
        </div>
      </div>
      <!-- <div class="w-full md:w-2/3 lg:w-3/4" v-if="!loading && !isSubmitted">
        <form @submit.prevent="submitForm">
          <div class="flex items-center mb-2">
            <input
              type="text"
              name="name"
              v-model="form.name"
              :placeholder="t('name')"
              class="flex-grow rounded-lg p-2 mr-2 h-11"
              required
              :class="errors.name ? 'border-red-500' : 'border-[#979797]'"
            />
            <div class="border border-[#979797] rounded-lg p-2 h-11">
              <Icon name="ph:user" size="24" color="#000" />
            </div>
          </div>
          <p class="text-red-500 text-xs my-2" v-if="errors.name">
            {{ errors.name }}
          </p>

          <div class="flex items-center mb-2">
            <vue-tel-input
              v-model="form.phone"
              class="flex-grow rounded-lg mr-2 h-11"
              mode="international"
              inputOptions=""
              required
              :class="errors.phone ? 'border-red-500' : 'border-[#979797]'"
            ></vue-tel-input>
            <div class="border border-[#979797] rounded-lg p-2 h-11">
              <Icon name="ic:twotone-phone" size="24" color="#000" />
            </div>
          </div>
          <p class="text-red-500 text-xs my-2" v-if="errors.phone">
            {{ errors.phone }}
          </p>

          <div class="flex items-center mb-2">
            <input
              type="email"
              name="email"
              v-model="form.email"
              :placeholder="t('email')"
              class="flex-grow p-2 rounded-lg mr-2 h-11"
              required
              :class="errors.email ? 'border-red-500' : 'border-[#979797]'"
            />
            <div class="border border-[#979797] rounded-lg p-2 h-11">
              <Icon name="ic:outline-email" size="24" color="#000" />
            </div>
          </div>
          <p class="text-red-500 text-xs my-2" v-if="errors.email">
            {{ errors.email }}
          </p>

          <div class="mb-2">
            <textarea
              name="comments"
              v-model="form.comments"
              :placeholder="t('comments')"
              class="w-full p-2 border-[#979797] rounded-lg"
              rows="5"
              required
            ></textarea>
          </div>
          <p class="text-red-500 text-xs my-2" v-if="errors.comments">
            {{ errors.comments }}
          </p>

          <div class="w-full flex justify-center">
            <button
              type="submit"
              class="w-1/3 py-2 rounded-lg text-white bg-[#F37832] hover:bg-[#f26922]"
            >
              <Icon name="prime:send" class="mb-0.5" size="24" color="#fff" />

              {{ $t("submit") }}
            </button>
          </div>
        </form>
      </div>

      <div v-else-if="!loading && isSubmitted">
        <img src="/img/thank-you.svg" />
      </div> -->
      <div class="w-full md:w-2/3 lg:w-3/4" id="hubspotform"></div>
    </div>
  </section>
</template>

<script setup>
import { ref } from "vue";
import { VueTelInput } from "vue-tel-input";
import "vue-tel-input/vue-tel-input.css";
import { useToast } from "vue-toastification";

const toast = useToast();
definePageMeta({ layout: "landing" });

const { t } = useI18n();
const loading = ref(false);
const form = ref({
  name: "",
  phone: "",
  email: "",
  comments: "",
});
const errors = ref({});
const config = useRuntimeConfig();
const isSubmitted = ref(false);

onMounted(() => {
  loading.value = true;
  const script = document.createElement("script");
  script.src = "//js-eu1.hsforms.net/forms/embed/v2.js";
  script.charset = "utf-8";
  script.type = "text/javascript";
  document.head.appendChild(script);

  if (window.hbspt) {
    window.hbspt.forms.create({
      region: "eu1",
      portalId: "143734300",
      formId: "f95c9584-f133-4de2-bb25-f708ccac8812",
      target: "#hubspotform",
    });
  }
  setTimeout(() => {
    loading.value = false;
  }, 2000);
});

const validateForm = () => {
  errors.value = {};
  console.log(form.value);
  // Trim the phone number of all spaces
  form.value.phone = form.value.phone.replace(/\s+/g, "");

  if (!form.value.name) {
    errors.value.name = "Name is required.";
  }

  // Updated pattern for a generic international phone number without spaces
  const phonePattern = /^\+?(?:[0-9] ?){6,14}[0-9]$/;
  if (!phonePattern.test(form.value.phone)) {
    errors.value.phone = "Please enter a valid phone number.";
  }

  const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailPattern.test(form.value.email)) {
    errors.value.email = "Please enter a valid email address.";
  }

  return Object.keys(errors.value).length === 0;
};

const submitForm = async () => {
  // Assuming you have these refs defined
  loading.value = true;
  const message = ref("");

  if (!validateForm()) {
    message.value = "Validation failed. Please check your inputs.";
    loading.value = false;
    return;
  }
  const apiUrl = config.public.apiBase + "/contact";

  try {
    const response = await fetch(apiUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(form.value),
    });

    if (!response.ok) {
      throw new Error("Network response was not ok");
    }

    const responseData = await response.json();
    Object.keys(form.value).forEach((key) => {
      form.value[key] = "";
      errors.value[key] = "";
    });
    isSubmitted.value = true;
    toast.success(t("feedbackSubmitted"));
  } catch (error) {
    toast.error(t("serverError"));
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
/* Additional styles if needed */
</style>
