<script setup lang="ts">
import { Dropdown, ListGroup, ListGroupItem } from "flowbite-vue";
const locale = useLocale();

const switchLocalePath = useSwitchLocalePath();

const languages = ["ka", "en", "ru", "el"];
</script>

<template>
  <dropdown placement="left">
    <template #trigger>
      <Button
        class="w-10 h-10 flex justify-center items-center font-bold bg-velvet"
        >{{ locale.toUpperCase() }}</Button
      >
    </template>
    <list-group>
      <template v-for="lang in languages" :key="lang">
        <list-group-item v-if="locale !== lang">
          <div
            @click="
              locale = lang;
              $i18n.locale = lang;
            "
            class="flex w-full p-1 pl-0 gap-2 items-center"
          >
            <img
              class="rounded-pill w-6"
              :src="`/img/locals/${lang}.png`"
              alt=""
            />
            <span class="font-semibold">{{ $t(lang) }}</span>
          </div>
        </list-group-item>
      </template>
    </list-group>
  </dropdown>
</template>
