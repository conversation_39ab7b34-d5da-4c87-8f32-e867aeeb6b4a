<template>
  <div class="h-[100vh] w-full text-[#6C6C6C]">
    <!-- Navigation Bar -->
    <nav class="bg-white p-8">
      <div class="container mx-auto">
        <div class="flex justify-between items-center">
          <div>
            <p class="text-4xl text-orange font-bold">MenuHub</p>
          </div>
          <div>
            <ul class="hidden lg:flex lg:space-x-4 items-center">
              <li>
                <nuxt-link
                  to="/"
                  class="text-black hover:text-orange hover:font-bold"
                  >{{ $t("home") }}</nuxt-link
                >
              </li>
              <li>
                <nuxt-link
                  to="/#why"
                  class="text-black hover:text-orange cursor-pointer hover:font-bold"
                  >{{ $t("whyMenuhub") }}</nuxt-link
                >
              </li>

              <li class="relative m-4 mx-2 mr-8 rounded-xl">
                <button
                  @click.stop="toggleDropdown"
                  type="button"
                  class="cursor-pointer flex hover:bg-gray-200 p-4 items-center justify-center text-black"
                >
                  <div class="flex">
                    <span class="block mr-1">{{
                      locales.find((loc) => loc.locale == locale).name
                    }}</span>
                    <i class="fa fa-chevron-down text-md mt-2"></i>
                  </div>
                </button>
                <client-only>
                  <ul
                    v-show="dropdownOpen"
                    ref="dropdownElement"
                    class="absolute right-0 mt-2 w-[15vw] rounded-3xl border border-gray-200 shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-20"
                  >
                    <li class="pl-0">
                      <ul class="m-0">
                        <li
                          v-for="loc in locales"
                          :key="loc.locale"
                          class="py-2 px-4 rounded-3xl text-center cursor-pointer hover:bg-gray-200 w-full"
                          :class="
                            locales.indexOf(loc) < locales.length - 1
                              ? `border-b border-gray-200`
                              : ''
                          "
                          @click="
                            locale = loc.locale;
                            dropdownOpen = false;
                            $i18n.locale = loc.locale;
                          "
                        >
                          <span
                            class="font-medium text-gray-500 inline-block mr-2"
                            v-text="loc.name"
                          ></span>
                          <span
                            class="font-bold"
                            v-text="loc.locale.toUpperCase()"
                          ></span>
                        </li>
                      </ul>
                    </li>
                  </ul>
                </client-only>
              </li>

              <li>
                <nuxt-link
                  to="contact"
                  class="rounded-lg border-orange border-2 p-2"
                >
                  {{ $t("contactUs") }}
                </nuxt-link>
              </li>
              <li>
                <nuxt-link
                  to="register"
                  class="text-white rounded-lg bg-orange p-3 px-4"
                >
                  {{ $t("register") }}
                </nuxt-link>
              </li>
            </ul>
          </div>
          <div class="lg:hidden flex items-center hover:cursor-pointer">
            <button @click.prevent="toggleMobDropdown" class="text-xl">
              <i class="fa fa-bars"></i>
            </button>
          </div>
        </div>
        <div v-show="mobDropdown" ref="dropdownMobElement" class="lg:hidden">
          <ul class="flex flex-col space-y-8 mt-4">
            <!-- Mobile Nav Items -->
            <li>
              <nuxt-link
                to="/"
                class="text-black hover:text-orange hover:font-bold block px-4 py-2"
                >{{ $t("home") }}</nuxt-link
              >
            </li>
            <li>
              <nuxt-link
                to="/#why"
                class="text-black hover:text-orange hover:font-bold block px-4 py-2"
                >{{ $t("whyMenuhub") }}</nuxt-link
              >
            </li>

            <li class="relative m-4 mx-2">
              <button
                @click.stop="toggleDropdown"
                type="button"
                class="cursor-pointer flex hover:bg-gray-200 p-2 items-center justify-center text-black"
              >
                <div class="flex">
                  <span class="block mr-1">{{
                    locales.find((loc) => loc.locale == locale).name
                  }}</span>
                  <i class="fa fa-chevron-down text-md mt-2"></i>
                </div>
              </button>
              <client-only>
                <ul
                  v-show="dropdownOpen"
                  @click.away="dropdownOpen = false"
                  class="absolute mt-2 w-[15vw] rounded-3xl border border-gray-200 shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-20"
                >
                  <li class="pl-0">
                    <ul class="m-0">
                      <li
                        v-for="loc in locales"
                        :key="loc.locale"
                        class="py-2 px-4 rounded-3xl text-center cursor-pointer hover:bg-gray-200 w-full"
                        :class="
                          locales.indexOf(loc) < locales.length - 1
                            ? `border-b border-gray-200`
                            : ''
                        "
                        @click="
                          locale = loc.locale;
                          dropdownOpen = false;
                          $i18n.locale = loc.locale;
                        "
                      >
                        <span
                          class="font-medium text-gray-500 inline-block mr-2"
                          v-text="loc.name"
                        ></span>
                        <span
                          class="font-bold"
                          v-text="loc.locale.toUpperCase()"
                        ></span>
                      </li>
                    </ul>
                  </li>
                </ul>
              </client-only>
            </li>
            <li class="my-4">
              <nuxt-link
                to="contact"
                class="rounded-lg border-orange border-2 ml-4 px-8 p-2"
              >
                {{ $t("contactUs") }}
              </nuxt-link>
            </li>
            <li class="my-4">
              <nuxt-link
                to="register"
                class="text-white rounded-lg bg-orange ml-4 px-8 p-2"
              >
                {{ $t("register") }}
              </nuxt-link>
            </li>
            <!-- ...rest of your mobile nav items here... -->
          </ul>
        </div>
      </div>
    </nav>

    <slot />

    <footer class="bg-white">
      <!-- First Row -->
      <div
        class="flex flex-col lg:flex-row items-center justify-between p-8 lg:px-12 bg-gray-400 w-full"
        id="footer-first-row"
      >
        <!-- Left Column: Links with icons -->
        <div class="flex flex-col lg:flex-row lg:space-x-4">
          <a
            :href="`tel:${config.public.domainPhone}`"
            class="flex items-center space-x-2 mb-4 lg:mb-0 hover:text-orange"
          >
            <i class="fa fa-phone text-2xl text-orange"></i>
            <span>{{ config.public.domainPhone }}</span>
          </a>

          <span class="h-4 hidden lg:inline">|</span>
          <a
            :href="`mailto:${config.public.domainEmail}`"
            class="flex items-center space-x-2 mb-4 lg:mb-0 hover:text-orange"
          >
            <i class="fa fa-envelope text-2xl text-orange"></i>
            <span>{{ config.public.domainEmail }}</span>
          </a>
          <span class="h-4 hidden lg:inline">|</span>
          <a
            href=""
            class="#flex items-center space-x-2 mb-4 lg:mb-0 hover:text-orange"
          >
            <i class="fa fa-map-marker text-2xl text-orange"></i>
            <span>{{ config.public.domainAddress }}</span>
          </a>
        </div>
        <!-- Right Column: Language Links -->
        <div class="flex space-x-4 px-4" id="locale-list">
          <div v-for="loc in locales" :key="loc.locale">
            <span
              class="mr-4 hover:font-bold hover:cursor-pointer"
              @click="
                locale = loc.locale;
                dropdownOpen = false;
                $i18n.locale = loc.locale;
              "
              >{{ loc.name }}</span
            >
            <span v-if="locales.indexOf(loc) < locales.length - 1">|</span>
          </div>
        </div>
      </div>
      <div class="py-4" id="footer-second-row">
        <div class="container mx-auto text-center">
          <div class="space-x-4">
            <nuxt-link to="terms" class="hover:underline mx-4">{{
              $t("termsOfUse")
            }}</nuxt-link>
            <span>|</span>
            <nuxt-link to="privacy" class="hover:underline mx-4">{{
              $t("privacyPolicy")
            }}</nuxt-link>
            <span>|</span>
            <nuxt-link to="cookie" class="hover:underline mx-4">{{
              $t("cookiePolicy")
            }}</nuxt-link>
          </div>
        </div>
      </div>
    </footer>
  </div>
</template>
<style>
:root {
  --color-primary: #f37832;
  --color-secondary: rgb(111 114 185);
  --color-primary-light: rgba(243, 120, 50, 0.02);
  --color-gray: #f8f5f4;
  --color-cream: #f3eae5;
}

nav a:hover {
  text-decoration: none;
}

#footer-first-row {
  background-color: var(--color-gray);
}

#footer-second-row {
  background-color: var(--color-cream);
  color: rgba(0, 0, 0, 0.3);
}

#locale-list {
  color: rgba(0, 0, 0, 0.25);
}

#subhead {
  color: var(--color-primary);
  font-size: 38px;
  font-weight: 700;
}
</style>
<script setup>
const locale = useLocale();
const { t } = useI18n();
const router = useRouter();
const locales = [
  { locale: "ka", name: "ქართული" },
  { locale: "en", name: "English" },
  { locale: "el", name: "Ελληνικά" },
  { locale: "ru", name: "Русский" },
];

const dropdownOpen = ref(false);
const toggleDropdown = () => {
  dropdownOpen.value = !dropdownOpen.value;
};

const mobDropdown = ref(false);

const toggleMobDropdown = () => {
  mobDropdown.value = !mobDropdown.value;
};

const windowWidth = ref(null);

onMounted(() => {
  if (typeof window !== "undefined") {
    windowWidth.value = window.innerWidth;
    window.addEventListener("resize", () => {
      windowWidth.value = window.innerWidth;
    });
    document.addEventListener("click", handleClickOutside);
  }
});

onBeforeUnmount(() => {
  if (typeof window !== "undefined") {
    window.removeEventListener("resize", () => {
      windowWidth.value = window.innerWidth;
    });
    document.removeEventListener("click", handleClickOutside);
  }
});

let dropdownElement = ref(null);
let dropdownMobElement = ref(null);
let dropdownParentElement = ref(null);
let dropdownParentMobElement = ref(null);
const handleClickOutside = (event) => {
  if (dropdownElement.value && !dropdownElement.value.contains(event.target)) {
    dropdownOpen.value = false;
  } else if (
    dropdownMobElement.value &&
    windowWidth.value <= 940 &&
    !dropdownMobElement.value.contains(event.target)
  ) {
    mobDropdown.value = false;
  }
};

const config = useRuntimeConfig();
</script>
