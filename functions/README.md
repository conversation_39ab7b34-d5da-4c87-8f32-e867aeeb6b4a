# URL Shortener API

This directory contains Cloudflare Functions for a URL shortening service.

## Setup

1. Create a KV namespace in your Cloudflare dashboard
2. Update the `wrangler.toml` file with your actual KV namespace IDs
3. Deploy to Cloudflare Pages

## API Endpoints

### POST /api/shorten

Creates a shortened URL from a long URL.

**Request:**
```bash
curl -X POST https://menuhub.me/api/shorten \
  -H "Content-Type: application/json" \
  -d '{"url": "https://a-long-url.with.parameters?here=value&another=another-value"}'
```

**Response:**
```
https://menuhub.me/api/r/Ux182734
```

### GET /api/r/{code}

Redirects to the original URL.

**Request:**
```bash
curl -L https://menuhub.me/api/r/Ux182734
```

**Response:**
- 302 redirect to the original URL
- 404 if the short code doesn't exist

## Features

- **Duplicate Prevention:** If the same URL is submitted multiple times, the same short code is returned
- **Robust Short Codes:** 8-character codes using alphanumeric characters (62^8 = ~218 trillion possibilities)
- **Error Handling:** Proper HTTP status codes and error messages
- **Caching:** Redirect responses are cached for 24 hours
- **URL Validation:** Ensures submitted URLs are valid before processing

## KV Storage Structure

The service uses two types of KV entries:

1. `code:{shortCode}` → `{originalUrl}` (for redirects)
2. `hash:{urlHash}` → `{shortCode}` (for duplicate prevention)

## Testing Locally

You can test the functions locally using Wrangler:

```bash
npx wrangler pages dev
```

Make sure to create a local KV namespace for testing:

```bash
npx wrangler kv:namespace create "URL" --preview
``` 