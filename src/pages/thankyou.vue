<template>
  <section class="pb-16 bg-white px-8 h-[75vh]" id="contact">
    <div class="container mx-auto flex items-center flex-col">
      <div class="text-center w-full mb-16">
        <!-- <CustomHeading :text="$t('thankYou')" /> -->
      </div>
      <div v-if="loading">
        <div class="text-center blue">
          <i class="fa fa-spin fa-spinner fa-5x"></i>
        </div>
      </div>
      <div v-else>
        <img src="/img/thank-you.svg" />
      </div>
    </div>
  </section>
</template>

<script setup>
import { ref } from "vue";
import { VueTelInput } from "vue-tel-input";
import "vue-tel-input/vue-tel-input.css";
import { useToast } from "vue-toastification";

const toast = useToast();
definePageMeta({ layout: "landing" });

const { t } = useI18n();
const loading = ref(false);
</script>

<style scoped>
/* Additional styles if needed */
</style>
