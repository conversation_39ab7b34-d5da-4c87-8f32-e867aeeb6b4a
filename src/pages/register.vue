<template>
  <!-- Registration Section -->
  <section class="pb-16 bg-white px-8" id="registration">
    <div class="container mx-auto flex items-center flex-col">
      <div class="text-center w-full mb-8 text-orange">
        <CustomHeading :text="$t('register')" />
      </div>
      <div v-if="loading">
        <div class="text-center text-orange">
          <i class="fa fa-spin fa-spinner fa-5x"></i>
        </div>
      </div>
      <!-- Registration Form -->
      <div class="w-full md:w-2/3 lg:w-3/4" v-if="!loading && !isSubmitted">
        <form @submit.prevent="submitForm">
          <!-- First Name and Last Name Fields -->
          <div class="flex items-center mb-4">
            <input
              type="text"
              name="firstName"
              v-model="form.firstName"
              :placeholder="t('firstName')"
              class="flex-grow rounded-lg p-2 mr-2 h-[3rem]"
              required
              :class="errors.firstName ? 'border-red-500' : 'border-[#979797]'"
            />
            <input
              type="text"
              name="lastName"
              v-model="form.lastName"
              :placeholder="t('lastName')"
              class="flex-grow rounded-lg p-2 ml-2 h-[3rem]"
              required
              :class="errors.lastName ? 'border-red-500' : 'border-[#979797]'"
            />
          </div>
          <p
            class="text-red-500 text-xs my-2"
            v-if="errors.firstName || errors.lastName"
          >
            {{ errors.firstName || errors.lastName }}
          </p>

          <!-- Restaurant Name Field -->
          <div class="mb-4">
            <input
              type="text"
              name="restaurantName"
              v-model="form.restaurantName"
              :placeholder="t('restaurantName')"
              class="w-full p-2 rounded-lg h-[3rem]"
              required
              :class="
                errors.restaurantName ? 'border-red-500' : 'border-[#979797]'
              "
            />
          </div>
          <p class="text-red-500 text-xs my-2" v-if="errors.restaurantName">
            {{ errors.restaurantName }}
          </p>

          <!-- Email Field -->
          <div class="flex items-center mb-4">
            <input
              type="email"
              name="email"
              v-model="form.email"
              :placeholder="t('email')"
              class="flex-grow p-2 rounded-lg mr-2 h-[3rem]"
              required
              :class="errors.email ? 'border-red-500' : 'border-[#979797]'"
            />
            <div class="border border-[#979797] rounded-lg p-2 h-[3rem]">
              <Icon name="ic:outline-email" size="24" color="#000" />
            </div>
          </div>
          <p class="text-red-500 text-xs my-2" v-if="errors.email">
            {{ errors.email }}
          </p>

          <!-- Phone Number Field -->
          <div class="flex items-center mb-4">
            <vue-tel-input
              v-model="form.phone"
              class="flex-grow rounded-lg mr-2 h-[3rem]"
              mode="international"
              inputOptions=""
              required
              :class="errors.phone ? 'border-red-500' : 'border-[#979797]'"
            ></vue-tel-input>
            <div class="border border-[#979797] rounded-lg p-2 h-[3rem]">
              <Icon name="ic:twotone-phone" size="24" color="#000" />
            </div>
          </div>
          <p class="text-red-500 text-xs my-2" v-if="errors.phone">
            {{ errors.phone }}
          </p>

          <!-- Password Fields -->
          <div class="mb-4">
            <input
              type="password"
              name="password"
              v-model="form.password"
              :placeholder="t('password')"
              class="w-full p-2 rounded-lg h-[3rem]"
              required
              :class="errors.password ? 'border-red-500' : 'border-[#979797]'"
            />
          </div>
          <p class="text-red-500 text-xs my-2" v-if="errors.password">
            {{ errors.password }}
          </p>
          <div class="mb-4">
            <input
              type="password"
              name="repeatPassword"
              v-model="form.repeatPassword"
              :placeholder="t('repeatPassword')"
              class="w-full p-2 rounded-lg h-[3rem]"
              required
              :class="
                errors.repeatPassword ? 'border-red-500' : 'border-[#979797]'
              "
            />
          </div>
          <p class="text-red-500 text-xs my-2" v-if="errors.repeatPassword">
            {{ errors.repeatPassword }}
          </p>

          <!-- Hotel Setup Checkbox -->
          <div
            class="flex items-center mb-4 mt-6 border p-2 rounded-lg"
            style="display: none"
            :class="errors.ishotel ? 'border-red-500' : 'border-[#979797]'"
          >
            <input
              type="checkbox"
              name="ishotel"
              id="ishotel"
              v-model="form.ishotel"
              class="hidden"
            />
            <label for="ishotel" class="checkbox-label">
              <img
                v-if="form.ishotel"
                src="/img/check-square.svg"
                alt="Checked"
                class="w-6 h-6"
              />
              <svg
                v-else
                class="w-6 h-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <rect
                  width="20"
                  height="20"
                  x="2"
                  y="2"
                  rx="5"
                  stroke-width="2"
                ></rect>
              </svg>
            </label>
            <label for="ishotel" class="ml-2">
              <div v-html="$t('setupForHotel')"></div>
            </label>
          </div>
          <p class="text-red-500 text-xs my-2" v-if="errors.ishotel">
            {{ errors.ishotel }}
          </p>

          <!-- Agree Checkbox -->
          <div class="flex items-center mb-4 justify-center mt-6">
            <input
              type="checkbox"
              name="agree"
              id="agree"
              v-model="form.agree"
              class="hidden"
            />
            <label for="agree" class="checkbox-label">
              <img
                v-if="form.agree"
                src="/img/check-square.svg"
                alt="Checked"
                class="w-6 h-6"
              />

              <svg
                v-else
                class="w-6 h-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <rect
                  width="20"
                  height="20"
                  x="2"
                  y="2"
                  rx="5"
                  stroke-width="2"
                ></rect>
              </svg>
            </label>
            <label for="agree" class="ml-2">
              <div v-html="$t('iAgree')"></div>
            </label>
          </div>
          <p class="text-red-500 text-xs my-2" v-if="errors.agree">
            {{ errors.agree }}
          </p>

          <!-- Submit Button -->
          <div class="w-full flex justify-center mt-8 mb-8">
            <button
              type="submit"
              class="w-1/3 py-2 rounded-lg text-white bg-orange hover:bg-[#f26922]"
            >
              <Icon name="prime:send" class="mb-0.5" size="24" color="#fff" />

              {{ $t("submit") }}
            </button>
          </div>
        </form>
      </div>

      <div v-else-if="!loading && isSubmitted">
        <img src="/img/thank-you.svg" />
      </div>
    </div>
  </section>
</template>

<script setup>

// onMounted( async () => {
//       await navigateTo("https://mnu-demo-e52bb80.fesvi.co/t/MQ", { external: true, });
// });

import { ref } from "vue";
import { VueTelInput } from "vue-tel-input";
import "vue-tel-input/vue-tel-input.css";
import { useToast } from "vue-toastification";

definePageMeta({ layout: "landing" });

const { t } = useI18n();
const toast = useToast();
const form = ref({
  firstName: "",
  lastName: "",
  restaurantName: "",
  email: "",
  phone: "",
  password: "",
  repeatPassword: "",
  agree: false,
  ishotel: false,
});
const errors = ref({});
const loading = ref(false);
const isSubmitted = ref(false);

const validateForm = () => {
  errors.value = {};
  form.value.phone = form.value.phone.replace(/\s+/g, "");

  if (!form.value.firstName || !form.value.lastName) {
    errors.value.name = t("nameRequired");
  }

  if (!form.value.restaurantName) {
    errors.value.restaurantName = t("restaurantNameRequired");
  }

  const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailPattern.test(form.value.email)) {
    errors.value.email = t("invalidEmail");
  }

  const phonePattern = /^\+?(?:[0-9] ?){6,14}[0-9]$/;
  if (!phonePattern.test(form.value.phone)) {
    errors.value.phone = t("invalidPhone");
  }
  form.value.phone = form.value.phone.replace(/\s+/g, "");

  if (!form.value.password || form.value.password.length < 8) {
    errors.value.password = t("passwordRequired");
  }

  if (form.value.password !== form.value.repeatPassword) {
    errors.value.repeatPassword = t("passwordMismatch");
  }

  if (!form.value.agree) {
    errors.value.agree = t("agreeRequired");
  }

  return Object.keys(errors.value).length === 0;
};

const submitForm = async () => {
  loading.value = true;
  if (!validateForm()) {
    toast.error(t("validationFailed"));
    loading.value = false;
    return;
  }

  const apiUrl = useRuntimeConfig().public.apiBase + "/register";

  try {
    const response = await fetch(apiUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(form.value),
    });

    const responseData = await response.json();
    if (
      !responseData.status &&
      responseData.message.toLowerCase().includes("email")
    ) {
      toast.error(t("emailExists"));
      return;
    } else if (!responseData.status) {
      throw Error("serverError");
    }
    errors.value = {};
    isSubmitted.value = true;
    toast.success(t("registerationSuccess"));
  } catch (error) {
    toast.error(t("serverError"));
  } finally {
    loading.value = false;
  }
};
</script>

<style>
.checkbox-label {
  display: inline-block;
  width: 24px;
  height: 24px;
  border-radius: 4px;
  cursor: pointer;
}
</style>
