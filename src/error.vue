<script setup>
const error = useError();
const route = useRoute();

// Enhanced error handling
const errorInfo = computed(() => {
  const statusCode = error.value?.statusCode || 500;
  const statusMessage = error.value?.statusMessage || 'Something went wrong';
  
  let title, description, action;
  
  switch (statusCode) {
    case 404:
      title = 'Page Not Found';
      description = 'The page you are looking for doesn\'t exist or has been moved.';
      action = 'Go Home';
      break;
    case 500:
      title = 'Server Error';
      description = 'We\'re experiencing some technical difficulties. Please try again later.';
      action = 'Try Again';
      break;
    default:
      title = `Error ${statusCode}`;
      description = statusMessage;
      action = 'Go Back';
  }
  
  return { statusCode, title, description, action, statusMessage };
});

const handleError = () => {
  clearError({ redirect: '/' });
};

const goBack = () => {
  if (window.history.length > 1) {
    window.history.back();
  } else {
    handleError();
  }
};

// Debug mode for development
const isDev = process.dev;
const showDebug = ref(false);

const debugInfo = computed(() => ({
  statusCode: error.value?.statusCode,
  statusMessage: error.value?.statusMessage,
  stack: error.value?.stack,
  url: error.value?.url,
  currentRoute: route.fullPath,
  timestamp: new Date().toISOString(),
}));
</script>

<template>
  <nuxt-layout>
    <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div class="max-w-2xl w-full">
        <!-- Main Error Card -->
        <div class="bg-white rounded-2xl shadow-xl p-8 text-center">
          <!-- Error Image -->
          <div class="mb-8">
            <img 
              class="mx-auto max-w-sm w-full h-auto" 
              src="/img/404.png" 
              :alt="`Error ${errorInfo.statusCode}`"
            >
          </div>
          
          <!-- Error Content -->
          <div class="space-y-6">
            <!-- Status Code -->
            <div class="text-6xl font-bold text-indigo-600">
              {{ errorInfo.statusCode }}
            </div>
            
            <!-- Title -->
            <h1 class="text-3xl font-bold text-gray-900">
              {{ errorInfo.title }}
            </h1>
            
            <!-- Description -->
            <p class="text-lg text-gray-600 max-w-md mx-auto">
              {{ errorInfo.description }}
            </p>
            
            <!-- Action Buttons -->
            <div class="flex flex-col sm:flex-row gap-4 justify-center items-center pt-4">
              <button 
                @click="handleError" 
                class="px-8 py-3 bg-indigo-600 hover:bg-indigo-700 text-white font-semibold rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
              >
                {{ errorInfo.action }}
              </button>
              
              <button 
                @click="goBack" 
                class="px-8 py-3 border-2 border-gray-300 hover:border-gray-400 text-gray-700 font-semibold rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
              >
                Go Back
              </button>
            </div>
          </div>
        </div>
        
        <!-- Debug Information (Development only) -->
        <div v-if="isDev" class="mt-6">
          <button 
            @click="showDebug = !showDebug"
            class="w-full text-sm text-gray-500 hover:text-gray-700 font-medium"
          >
            {{ showDebug ? 'Hide' : 'Show' }} Debug Info
          </button>
          
          <div v-if="showDebug" class="mt-4 bg-gray-900 text-gray-100 rounded-lg p-4 text-left">
            <h3 class="text-lg font-semibold mb-3 text-yellow-400">Debug Information</h3>
            <pre class="text-sm overflow-x-auto whitespace-pre-wrap">{{ JSON.stringify(debugInfo, null, 2) }}</pre>
          </div>
        </div>
        
        <!-- Help Section -->
        <div class="mt-8 text-center">
          <p class="text-sm text-gray-500">
            Need help? 
            <a href="/contact" class="text-indigo-600 hover:text-indigo-700 font-medium">
              Contact Support
            </a>
          </p>
        </div>
      </div>
    </div>
  </nuxt-layout>
</template>

<style scoped>
/* Additional custom styles if needed */
.error-animation {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
