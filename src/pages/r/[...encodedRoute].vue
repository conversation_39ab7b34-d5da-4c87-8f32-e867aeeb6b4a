<template>
  <div v-if="loading" class="flex items-center justify-center min-h-screen">
    <div class="text-lg">Redirecting...</div>
  </div>
  <div v-else-if="error" class="flex items-center justify-center min-h-screen">
    <div class="text-center p-8">
      <h1 class="text-2xl font-bold text-red-600 mb-4">Redirect Error</h1>
      <p class="text-gray-600 mb-4">{{ errorMessage }}</p>
      <div v-if="debugInfo" class="text-sm text-gray-500 bg-gray-100 p-4 rounded">
        <pre>{{ debugInfo }}</pre>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { useRoute } from "vue-router";

const loading = ref(true);
const error = ref(false);
const errorMessage = ref("");
const debugInfo = ref("");

// Function to properly decode URL-safe base64
const decodeBase64Url = (str) => {
  // Replace URL-safe characters with regular base64 characters
  let base64 = str.replace(/-/g, '+').replace(/_/g, '/');
  
  // Add padding if necessary
  while (base64.length % 4) {
    base64 += '=';
  }
  
  return atob(base64);
};

// Function to handle different encoding scenarios
const processEncodedUrl = (encodedParam) => {
  console.log("Processing encoded parameter:", encodedParam);
  
  let decodedUrl;
  
  try {
    // Try direct base64 decode first (for properly formatted URLs)
    decodedUrl = atob(encodedParam);
    console.log("Direct atob succeeded:", decodedUrl);
  } catch (e1) {
    try {
      // Try URL-safe base64 decode
      decodedUrl = decodeBase64Url(encodedParam);
      console.log("URL-safe base64 decode succeeded:", decodedUrl);
    } catch (e2) {
      try {
        // Try URL decoding first, then base64
        const urlDecoded = decodeURIComponent(encodedParam);
        decodedUrl = atob(urlDecoded);
        console.log("URL decode + atob succeeded:", decodedUrl);
      } catch (e3) {
        try {
          // Try URL decoding first, then URL-safe base64
          const urlDecoded = decodeURIComponent(encodedParam);
          decodedUrl = decodeBase64Url(urlDecoded);
          console.log("URL decode + URL-safe base64 succeeded:", decodedUrl);
        } catch (e4) {
          throw new Error(`All decoding methods failed. Original: ${encodedParam}`);
        }
      }
    }
  }
  
  return decodedUrl;
};

onMounted(async () => {
  const route = useRoute();
  const encodedRouteArray = route.params.encodedRoute;

  console.log("=== URL Redirect Debug ===");
  console.log("Raw route param:", encodedRouteArray);
  console.log("Is array:", Array.isArray(encodedRouteArray));
  
  try {
    // Handle catch-all route parameters - join the array back together
    let encodedString;
    if (Array.isArray(encodedRouteArray)) {
      // Join all segments with '/' to reconstruct the original base64 string
      encodedString = encodedRouteArray.join('/');
      console.log("Reconstructed from array:", encodedString);
    } else {
      encodedString = encodedRouteArray;
      console.log("Single parameter:", encodedString);
    }
    
    if (!encodedString) {
      throw new Error("No encoded route parameter provided");
    }

    // Process the encoded URL with multiple fallback methods
    const decodedUrl = processEncodedUrl(encodedString);
    console.log("Final decoded URL:", decodedUrl);

    // Basic URL validation
    if (!decodedUrl.startsWith('http://') && !decodedUrl.startsWith('https://')) {
      throw new Error(`Invalid URL format - must start with http:// or https://: ${decodedUrl}`);
    }

    // Attempt redirect
    console.log("Redirecting to:", decodedUrl);
    window.location.href = decodedUrl;
    
  } catch (err) {
    console.error("=== Redirect Error ===", err);
    
    // Prepare debug information
    const debug = {
      originalParam: encodedRouteArray,
      reconstructedParam: Array.isArray(encodedRouteArray) ? encodedRouteArray.join('/') : encodedRouteArray,
      paramType: typeof encodedRouteArray,
      isArray: Array.isArray(encodedRouteArray),
      error: err.message,
      timestamp: new Date().toISOString()
    };
    
    debugInfo.value = JSON.stringify(debug, null, 2);
    errorMessage.value = err.message;
    error.value = true;
    loading.value = false;

    // Redirect to error page after delay
    setTimeout(() => {
      throw createError({
        statusCode: 404,
        statusMessage: `Invalid Redirect URL: ${err.message}`,
        fatal: true,
      });
    }, 5000);
  }
});
</script>
