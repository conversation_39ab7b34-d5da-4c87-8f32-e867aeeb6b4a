name: Deployment Workflow

on:
  push:
    branches:
      - main

jobs:
  build-and-push:
    runs-on: ubuntu-latest
    steps:
      - name: Syncing the forks
        if: github.repository == 'fesvi/menuhub' # ${{ github.event.repository.name }}
        run: |
          echo "Nothing really happens here anymore..."
          # gh repo sync fesvi/gr-menuhub
          # gh repo sync fesvi/uk-menuhub
        env:
          GH_TOKEN: ${{ secrets.GHCR_TOKEN }}

