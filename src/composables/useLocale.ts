import { ref, watchEffect } from "vue";

export const useLocale = () => {
  // We initialize locale from the cookie, or default to 'en' if the cookie doesn't exist
  const localeCookie = useCookie("locale");
  const locale = ref<string>(localeCookie.value || "en");

  // Whenever locale changes, we update the cookie
  watchEffect(() => {
    localeCookie.value = locale.value;
  });

  return locale;
};
