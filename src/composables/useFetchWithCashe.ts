import { StorageSerializers, useSessionStorage } from "@vueuse/core";

export default async <T>(url: string, options: any) => {
  const error = ref<any>(null);
  const pending = ref<any>(null);

  const cashed = useSessionStorage<T>(url, null, {
    serializer: StorageSerializers.object,
  });

  if (!cashed.value) {
    const {
      data,
      error: fetchError,
      pending: fetchPending,
    } = await useFetch<T>(url, options);

    cashed.value = data.value as T;
    pending.value = fetchPending;
    error.value = fetchError;
  }

  return { cashed, error };
};
