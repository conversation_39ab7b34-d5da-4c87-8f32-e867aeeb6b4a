<template>
  <div class="text-[28px] w-full text-main">
    <!-- Masthead Section -->
    <section class="relative h-screen bg-cover bg-center" id="masthead">
      <div class="absolute inset-0 bg-black opacity-50"></div>
      <div class="relative flex items-center h-full text-white">
        <h1
          class="text-[2.5rem] font-bold md:text-[3rem] lg:text-[62px] ml-16 md:w-[60%] w-[75%]"
          v-html="$t('maximizePotential')"
        ></h1>
      </div>
    </section>

    <!-- Menu Digitization Section -->
    <section class="py-4 bg-white px-8">
      <div
        class="container mx-auto flex items-center justify-center xs:text-center md:text-left flex-col"
      >
        <div class="text-center w-full mb-8">
          <CustomHeading :text="$t('menuDigitization')" />
        </div>
        <div class="flex flex-wrap -mx-4 py-16">
          <div class="w-full md:w-1/2 px-4 mb-8 md:mb-0">
            <h3 class="mb-4" id="subhead">
              {{ $t("menuDigitizationSubhead") }}
            </h3>
            <p>
              {{ $t("menuDigitizationContent") }}
            </p>
          </div>
          <div class="w-full md:w-1/2 px-4">
            <img
              src="/img/ordering.png"
              alt="Ordering with phones Image"
              class="w-full h-auto"
            />
          </div>
        </div>
      </div>
    </section>

    <!--Enhance Secction-->
    <section class="py-4 bg-white px-8 relative">
      <div class="container mx-auto flex items-center flex-col">
        <div class="text-center w-full">
          <CustomHeading :text="$t('enhanceHead')" />
        </div>
        <p class="relative top-[-2rem] text-center">
          {{ $t("enhanceContent") }}
        </p>
      </div>
    </section>
    <section class="grid grid-cols-1 gap-8 mx-4 md:mx-0">
      <!-- First Row -->
      <div
        class="grid grid-cols-1 md:grid-cols-2 gap-8 md:ml-0 md:mr-[5vw] items-stretch"
      >
        <!-- First Column -->
        <div
          class="flex flex-col bg-white p-6 border-lightOrange border rounded-3xl md:rounded-tl-none md:rounded-bl-none roundedShadow"
        >
          <div class="flex items-center mb-4">
            <div class="bg-orange-500 p-2 rounded-full mr-3">
              <Icon name="fluent:tv-20-regular" size="35" color="#f37382" />
            </div>
            <h2 class="text-xl font-semibold smallerSubhead">
              {{ $t("digitizeYourMenu") }}
            </h2>
          </div>
          <p class="flex-grow">
            {{ $t("browseRestaurants") }}
          </p>
        </div>

        <!-- Second Column -->
        <div
          class="flex flex-col bg-white p-6 border-lightOrange border roundedShadow rounded-3xl"
        >
          <div class="flex items-center mb-4">
            <div class="bg-orange-500 p-2 rounded-full mr-3">
              <Icon
                name="lucide:kanban-square-dashed"
                size="35"
                color="#f37382"
              />
            </div>
            <h2 class="text-xl font-semibold smallerSubhead">
              {{ $t("manageWithEase") }}
            </h2>
          </div>
          <p class="flex-grow">
            {{ $t("manageWithEaseContent") }}
          </p>
        </div>
      </div>

      <div
        class="grid grid-cols-1 md:grid-cols-2 gap-8 md:mr-0 md:ml-[5vw] items-stretch"
      >
        <!-- Third Column -->
        <div
          class="flex flex-col bg-white p-6 border-lightOrange border roundedShadow rounded-3xl"
        >
          <div class="flex items-center mb-4">
            <div class="bg-orange-500 p-2 rounded-full mr-3">
              <Icon name="lucide:user-check-2" size="35" color="#f37382" />
            </div>
            <h2 class="text-xl font-semibold smallerSubhead">
              {{ $t("enhanceHead") }}
            </h2>
          </div>
          <p class="flex-grow">
            {{ $t("enhanceContent") }}
          </p>
        </div>

        <!-- Fourth Column -->
        <div
          class="flex flex-col bg-white p-6 border-lightOrange border roundedShadow rounded-3xl md:rounded-tr-none md:rounded-br-none"
        >
          <div class="flex items-center mb-4">
            <div class="bg-orange-500 p-2 rounded-full mr-3">
              <Icon name="lucide:trending-up" size="35" color="#f37382" />
            </div>
            <h2 class="text-xl font-semibold smallerSubhead">
              {{ $t("promoteYourBrand") }}
            </h2>
          </div>
          <p class="flex-grow">{{ $t("promoteContent") }}</p>
        </div>
      </div>
    </section>

    <section class="py-4 bg-white px-8 relative" id="why">
      <div class="container mx-auto flex items-center flex-col">
        <div class="text-center w-full">
          <CustomHeading :text="$t('whyChooseUs')" />
        </div>
        <p class="relative top-[-2rem] text-center">
          {{ $t("whyChooseUsContent") }}
        </p>
      </div>
    </section>

    <!-- Why Choose Us Cards-->
    <section class="flex flex-wrap justify-center p-8 gap-10">
      <!-- Card 1 -->
      <div
        class="relative flex flex-col p-12 bg-white border-2 border-lightOrange rounded-3xl w-[320px]"
      >
        <div
          class="absolute top-[-32px] right-[24px] w-[64px] h-[64px] text-[64px] font-bold flex items-center justify-center bg-white rounded-full text-orange text-2xl font-bold"
        >
          1
        </div>
        <h3 class="smallerSubhead mb-2">
          {{ $t("seamlessIntegration") }}
        </h3>
        <p class="text-[24px]">{{ $t("seamlessIntegrationContent") }}</p>
      </div>

      <!-- Card 2 -->
      <div
        class="relative flex flex-col p-12 bg-white border-2 border-lightOrange rounded-3xl w-[320px]"
      >
        <div
          class="absolute top-[-32px] right-[24px] w-[64px] h-[64px] text-[64px] font-bold flex items-center justify-center bg-white rounded-full text-orange text-2xl font-bold"
        >
          2
        </div>
        <h3 class="smallerSubhead mb-2">
          {{ $t("noObligationTrial") }}
        </h3>
        <p class="text-[24px]">{{ $t("noObligationTrialContent") }}</p>
      </div>

      <!-- Card 3 -->
      <div
        class="relative flex flex-col p-12 bg-white border-2 border-lightOrange rounded-3xl w-[320px]"
      >
        <div
          class="absolute top-[-32px] right-[24px] w-[64px] h-[64px] text-[64px] font-bold flex items-center justify-center bg-white rounded-full text-orange text-2xl font-bold"
        >
          3
        </div>
        <h3 class="smallerSubhead mb-2">{{ $t("tailoredSupport") }}</h3>
        <p class="text-[24px]">{{ $t("tailoredSupportContent") }}</p>
      </div>

      <!-- Card 4 -->
      <div
        class="relative flex flex-col p-12 bg-white border-2 border-lightOrange rounded-3xl w-[320px]"
      >
        <div
          class="absolute top-[-32px] right-[24px] w-[64px] h-[64px] text-[64px] font-bold flex items-center justify-center bg-white rounded-full text-orange text-2xl font-bold"
        >
          4
        </div>
        <h3 class="smallerSubhead mb-2">
          {{ $t("transparentPricing") }}
        </h3>
        <p class="text-[24px]">{{ $t("transparentPricingContent") }}</p>
      </div>
    </section>

    <!-- Begin your success Journey -->
    <section class="py-4 bg-white px-8 relative">
      <div class="container mx-auto flex items-center flex-col">
        <div class="text-center w-full text-[38px]">
          <CustomHeading :text="$t('beginYourSuccesJourney')" />
        </div>
        <p class="relative top-[-2rem] text-center">
          {{ $t("beginYourSuccesJourneyContent") }}
        </p>
      </div>
      <div class="flex flex-col items-center p-8 space-y-6">
        <!-- Pricing Cards -->
        <div
          class="flex flex-col md:flex-row md:space-x-6 md:space-y-0 space-x-0 space-y-6"
        >
          <!-- Monthly -->
          <div
            class="flex flex-col items-center p-6 bg-white border-2 border-lightOrange rounded-lg w-64"
          >
            <h4
              class="mb-2 text-orange font-semibold border-b-2 border-orange w-full pb-2 text-center"
            >
              {{ $t("monthly") }}
            </h4>

            <span class="text-[44px] text-orange font-bold mb-2">{{
              config.public.monthlyPrice
            }}</span>
            <p class="text-stone text-[22px]">{{ $t("perMonth") }}</p>
          </div>

          <!-- Quarterly -->
          <div
            class="flex flex-col items-center p-6 bg-white border-2 border-lightOrange rounded-lg w-64"
          >
            <h4
              class="mb-2 text-orange font-semibold border-b-2 border-orange w-full pb-2 text-center"
            >
              {{ $t("quarterly") }}
            </h4>
            <span class="text-[44px] text-orange font-bold mb-2">{{
              config.public.quarterlyPrice
            }}</span>
            <p class="text-stone text-[22px]">{{ $t("threeMonths") }}</p>
          </div>

          <!-- Yearly -->
          <div
            class="flex flex-col items-center p-6 bg-white border-2 border-lightOrange rounded-lg w-64"
          >
            <h4
              class="mb-2 text-orange font-semibold border-b-2 border-orange w-full pb-2 text-center"
            >
              {{ $t("yearly") }}
            </h4>
            <span class="text-[44px] text-orange font-bold mb-2">{{
              config.public.yearlyPrice
            }}</span>
            <p class="text-stone text-[22px]">{{ $t("perYear") }}</p>
          </div>
        </div>

        <p class="text-[22px] text-stone mb-4">
          {{ $t("managedBy") }}<span class="font-bold">Fesvi.Co</span>
        </p>

        <p class="text-lg text-center mb-6">
          {{ $t("takeTheFirstStep") }}
        </p>

        <!-- Action Buttons -->
        <div class="flex space-x-4 justify-center items-center">
          <nuxt-link
            to="register"
            class="px-10 text-xl py-2 bg-orange hover:no-underline text-white rounded-lg font-semibold hover:bg-orange"
          >
            {{ $t("registerNow") }}
          </nuxt-link>
          <span class="text-center text-stone">{{ $t("or") }}</span>
          <nuxt-link
            to="contact"
            class="px-10 text-xl py-2 border-2 hover:no-underline border-orange text-orange rounded-lg font-semibold hover:bg-orange-100"
          >
            {{ $t("contactUs") }}
          </nuxt-link>
        </div>
      </div>
    </section>
  </div>
</template>

<style>
.badge {
  transform: translate(50%, -50%);
}

#masthead {
  background-image: url("/img/masthead_why.png");
}
.smallerSubhead {
  color: var(--color-primary);
  font-size: 28px;
  font-weight: 700;
}

.icon-card > div {
  border-radius: 1rem;
  background-color: rgba(243, 120, 50, 0.02);
  border-width: 2px;
  border-color: rgba(243, 120, 50, 0.2);
}

.roundedShadow {
  box-shadow: 3px 3px 8px 0px rgba(176, 63, 3, 0.04),
    10px 10px 14px 0px rgba(176, 63, 3, 0.03),
    23px 23px 19px 0px rgba(176, 63, 3, 0.02),
    41px 41px 23px 0px rgba(176, 63, 3, 0.01),
    64px 64px 25px 0px rgba(176, 63, 3, 0);
}
</style>
<script setup>
definePageMeta({ layout: "landing" });

const config = useRuntimeConfig();
const { t } = useI18n();
</script>
