import { createI18n } from "vue-i18n";
import en from "../locales/en";
import ka from "../locales/ka";
import ru from "../locales/ru";
import el from "../locales/el";

export default defineNuxtPlugin(({ vueApp }) => {
  const locale = useLocale();
  const i18n = createI18n({
    legacy: false,
    globalInjection: true,
    locale: locale.value,
    fallbackLocale: "en",
    messages: {
      en: en,
      ka: ka,
      ru: ru,
      el: el,
    },
  });

  // Watch for changes in locale and update i18n global locale accordingly
  watch(locale, (newLocale) => {
    i18n.global.locale.value = newLocale as "en" | "ka" | "ru" | "el";
  });

  vueApp.use(i18n);
});
