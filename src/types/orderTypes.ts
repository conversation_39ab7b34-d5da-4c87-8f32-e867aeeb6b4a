import { Restaurant, Dish } from "./commonTypes";
import { Table } from "./table-types";

export interface OrderData {
  status?: boolean;
  message?: string;
  data?: Order[];
}

export interface Order {
  id?: number;
  restaurant_id?: number;
  table_id?: number;
  quantity?: number;
  status?: number;
  started_at?: string;
  ended_at?: string | null;
  created_at?: string;
  updated_at?: string;
  restaurant?: Restaurant;
  table?: Table;
  dish?: Dish[];
}
